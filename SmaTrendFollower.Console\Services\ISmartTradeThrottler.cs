using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Smart trade throttler service for limiting trade frequency and preventing overtrading
/// Limits trade frequency per symbol, implements tick-range based throttling,
/// and prevents overtrading in noisy symbols or crowded setups
/// </summary>
public interface ISmartTradeThrottler : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when a trade is throttled/blocked
    /// </summary>
    event EventHandler<TradeThrottledEventArgs>? TradeThrottled;
    
    /// <summary>
    /// Fired when throttling rules are updated
    /// </summary>
    event EventHandler<ThrottlingRulesUpdatedEventArgs>? ThrottlingRulesUpdated;
    
    /// <summary>
    /// Fired when a cooldown period expires
    /// </summary>
    event EventHandler<CooldownExpiredEventArgs>? CooldownExpired;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start trade throttling for specified symbols
    /// </summary>
    Task StartThrottlingAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop trade throttling
    /// </summary>
    Task StopThrottlingAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if a trade should be allowed for a symbol
    /// </summary>
    Task<TradeThrottleDecision> ShouldAllowTradeAsync(string symbol, TradeRequest tradeRequest);
    
    /// <summary>
    /// Record a trade execution for throttling tracking
    /// </summary>
    Task RecordTradeExecutionAsync(string symbol, TradeExecution execution);
    
    /// <summary>
    /// Get current throttling status for a symbol
    /// </summary>
    Task<ThrottlingStatus?> GetThrottlingStatusAsync(string symbol);
    
    /// <summary>
    /// Get trade frequency statistics for a symbol
    /// </summary>
    Task<TradeFrequencyStats?> GetTradeFrequencyStatsAsync(string symbol);
    
    /// <summary>
    /// Override throttling for a specific symbol (emergency trades)
    /// </summary>
    Task OverrideThrottlingAsync(string symbol, TimeSpan duration, string reason);
    
    /// <summary>
    /// Reset throttling state for a symbol
    /// </summary>
    Task ResetThrottlingAsync(string symbol);
    
    /// <summary>
    /// Get recent throttling events for a symbol
    /// </summary>
    Task<IEnumerable<ThrottlingEvent>> GetRecentThrottlingEventsAsync(string symbol, int hours = 24);
    
    // === Configuration ===
    
    /// <summary>
    /// Update throttling configuration
    /// </summary>
    Task UpdateConfigurationAsync(TradeThrottlerConfig config);
    
    /// <summary>
    /// Update symbol-specific throttling rules
    /// </summary>
    Task UpdateSymbolRulesAsync(string symbol, SymbolThrottlingRules rules);
    
    /// <summary>
    /// Get throttling status
    /// </summary>
    ThrottlingServiceStatus GetStatus();
    
    /// <summary>
    /// Get list of throttled symbols
    /// </summary>
    IEnumerable<string> GetThrottledSymbols();
    
    /// <summary>
    /// Get list of monitored symbols
    /// </summary>
    IEnumerable<string> GetMonitoredSymbols();
}

/// <summary>
/// Trade throttler configuration
/// </summary>
public record TradeThrottlerConfig(
    int MaxTradesPerHour = 10,
    int MaxTradesPerDay = 50,
    TimeSpan MinTimeBetweenTrades = default,
    decimal MaxPositionSizePercent = 0.02m, // 2% of account
    bool EnableTickRangeThrottling = true,
    bool EnableVolatilityThrottling = true,
    bool EnableCrowdedSetupDetection = true,
    decimal VolatilityThreshold = 0.03m, // 3% volatility threshold
    TimeSpan CooldownPeriod = default,
    bool EnableOverrideCapability = true,
    TimeSpan StatisticsWindow = default
)
{
    public TimeSpan MinTimeBetweenTrades { get; init; } = MinTimeBetweenTrades == default ? TimeSpan.FromMinutes(5) : MinTimeBetweenTrades;
    public TimeSpan CooldownPeriod { get; init; } = CooldownPeriod == default ? TimeSpan.FromMinutes(30) : CooldownPeriod;
    public TimeSpan StatisticsWindow { get; init; } = StatisticsWindow == default ? TimeSpan.FromHours(24) : StatisticsWindow;
}

/// <summary>
/// Symbol-specific throttling rules
/// </summary>
public record SymbolThrottlingRules(
    string Symbol,
    int? MaxTradesPerHour = null,
    int? MaxTradesPerDay = null,
    TimeSpan? MinTimeBetweenTrades = null,
    decimal? MaxPositionSizePercent = null,
    decimal? TickRangeThreshold = null,
    bool? EnableSpecialHandling = null,
    Dictionary<string, object>? CustomParameters = null
);

/// <summary>
/// Trade request for throttling evaluation
/// </summary>
public record TradeRequest(
    string Symbol,
    TradeDirection Direction,
    decimal Quantity,
    decimal Price,
    decimal CurrentVolatility,
    decimal TickRange,
    TradeSignalStrength SignalStrength,
    DateTime RequestTime,
    string? SignalSource = null,
    Dictionary<string, object>? Metadata = null
);

/// <summary>
/// Trade execution record for throttling tracking
/// </summary>
public record TradeExecution(
    string Symbol,
    TradeDirection Direction,
    decimal ExecutedQuantity,
    decimal ExecutedPrice,
    DateTime ExecutionTime,
    string ExecutionId,
    decimal Slippage,
    TimeSpan ExecutionLatency
);

/// <summary>
/// Trade throttle decision result
/// </summary>
public record TradeThrottleDecision(
    bool IsAllowed,
    ThrottleReason Reason,
    string ReasonDescription,
    TimeSpan? SuggestedDelay,
    decimal ConfidenceLevel,
    DateTime DecisionTime,
    ThrottlingMetrics Metrics
);

/// <summary>
/// Current throttling status for a symbol
/// </summary>
public record ThrottlingStatus(
    string Symbol,
    bool IsThrottled,
    ThrottleReason CurrentReason,
    DateTime? ThrottleStartTime,
    DateTime? ThrottleEndTime,
    int TradesInLastHour,
    int TradesInLastDay,
    DateTime? LastTradeTime,
    TimeSpan? TimeUntilNextAllowed,
    bool IsInCooldown,
    bool HasOverride
);

/// <summary>
/// Trade frequency statistics
/// </summary>
public record TradeFrequencyStats(
    string Symbol,
    int TotalTrades,
    int TradesLastHour,
    int TradesLastDay,
    decimal AverageTimeBetweenTrades,
    decimal TradeFrequencyTrend,
    DateTime LastTradeTime,
    decimal AveragePositionSize,
    decimal WinRate,
    DateTime StatisticsTime
);

/// <summary>
/// Throttling event record
/// </summary>
public record ThrottlingEvent(
    string Symbol,
    ThrottleEventType EventType,
    ThrottleReason Reason,
    string Description,
    DateTime EventTime,
    TradeRequest? RelatedRequest = null,
    TimeSpan? Duration = null
);

/// <summary>
/// Throttling metrics
/// </summary>
public record ThrottlingMetrics(
    decimal CurrentVolatility,
    decimal TickRange,
    int RecentTradeCount,
    decimal PositionSizeRatio,
    decimal CrowdingScore,
    DateTime MetricsTime
);

/// <summary>
/// Trade direction enumeration
/// </summary>
public enum TradeDirection
{
    Buy,
    Sell,
    Cover,
    Short
}

/// <summary>
/// Trade signal strength
/// </summary>
public enum TradeSignalStrength
{
    Weak,
    Moderate,
    Strong,
    VeryStrong
}

/// <summary>
/// Throttle reason enumeration
/// </summary>
public enum ThrottleReason
{
    None,
    FrequencyLimit,
    TimeBetweenTrades,
    PositionSizeLimit,
    VolatilityThreshold,
    TickRangeLimit,
    CrowdedSetup,
    CooldownPeriod,
    SystemOverride,
    InsufficientSignalStrength,
    RiskManagement
}

/// <summary>
/// Throttle event type
/// </summary>
public enum ThrottleEventType
{
    TradeBlocked,
    TradeAllowed,
    CooldownStarted,
    CooldownEnded,
    OverrideApplied,
    OverrideExpired,
    RulesUpdated,
    ThresholdExceeded
}

/// <summary>
/// Throttling service status
/// </summary>
public enum ThrottlingServiceStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

// === Event Args ===

public class TradeThrottledEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required TradeRequest Request { get; init; }
    public required TradeThrottleDecision Decision { get; init; }
    public required DateTime ThrottleTime { get; init; }
}

public class ThrottlingRulesUpdatedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required SymbolThrottlingRules OldRules { get; init; }
    public required SymbolThrottlingRules NewRules { get; init; }
    public required DateTime UpdateTime { get; init; }
}

public class CooldownExpiredEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required TimeSpan CooldownDuration { get; init; }
    public required DateTime ExpirationTime { get; init; }
    public required ThrottleReason OriginalReason { get; init; }
}
